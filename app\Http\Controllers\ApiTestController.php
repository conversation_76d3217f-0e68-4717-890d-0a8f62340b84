<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ApiTestController extends Controller
{
    // In-memory storage for demo purposes (in real app, use database)
    private static $items = [
        ['id' => 1, 'name' => 'Item 1', 'description' => 'First test item'],
        ['id' => 2, 'name' => 'Item 2', 'description' => 'Second test item'],
    ];

    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => 'Items retrieved successfully',
            'data' => self::$items
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string|max:500'
        ]);

        $newId = count(self::$items) > 0 ? max(array_column(self::$items, 'id')) + 1 : 1;
        
        $newItem = [
            'id' => $newId,
            'name' => $request->name,
            'description' => $request->description
        ];

        self::$items[] = $newItem;

        return response()->json([
            'success' => true,
            'message' => 'Item created successfully',
            'data' => $newItem
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        $item = collect(self::$items)->firstWhere('id', $id);

        if (!$item) {
            return response()->json([
                'success' => false,
                'message' => 'Item not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Item retrieved successfully',
            'data' => $item
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string|max:500'
        ]);

        $itemIndex = array_search($id, array_column(self::$items, 'id'));

        if ($itemIndex === false) {
            return response()->json([
                'success' => false,
                'message' => 'Item not found'
            ], 404);
        }

        if ($request->has('name')) {
            self::$items[$itemIndex]['name'] = $request->name;
        }

        if ($request->has('description')) {
            self::$items[$itemIndex]['description'] = $request->description;
        }

        return response()->json([
            'success' => true,
            'message' => 'Item updated successfully',
            'data' => self::$items[$itemIndex]
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        $itemIndex = array_search($id, array_column(self::$items, 'id'));

        if ($itemIndex === false) {
            return response()->json([
                'success' => false,
                'message' => 'Item not found'
            ], 404);
        }

        $deletedItem = self::$items[$itemIndex];
        array_splice(self::$items, $itemIndex, 1);

        return response()->json([
            'success' => true,
            'message' => 'Item deleted successfully',
            'data' => $deletedItem
        ]);
    }

    /**
     * Simple health check endpoint
     */
    public function health(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => 'API is working!',
            'timestamp' => now()->toISOString(),
            'version' => '1.0.0'
        ]);
    }
}
