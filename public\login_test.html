<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Form Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }
        
        .form-section {
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .info-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        input[type="email"],
        input[type="password"],
        input[type="text"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="email"]:focus,
        input[type="password"]:focus,
        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
        }
        
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .response.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .response.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #e1e5e9;
        }
        
        .tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 600;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .info-section h2 {
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .demo-credentials {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .demo-credentials h3 {
            margin-bottom: 15px;
        }
        
        .credential-item {
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
        
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                max-width: 400px;
            }
            
            .info-section {
                order: -1;
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-section">
            <h1>🔐 Login Test</h1>
            <p class="subtitle">Test the authentication API endpoints</p>
            
            <div class="tabs">
                <div class="tab active" onclick="switchTab('login')">Login</div>
                <div class="tab" onclick="switchTab('register')">Register</div>
                <div class="tab" onclick="switchTab('profile')">Profile</div>
            </div>
            
            <!-- Login Tab -->
            <div id="login-tab" class="tab-content active">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="loginEmail">Email:</label>
                        <input type="email" id="loginEmail" value="<EMAIL>" required>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">Password:</label>
                        <input type="password" id="loginPassword" value="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Login</button>
                    <button type="button" class="btn btn-secondary" onclick="testHealth()">Test Health</button>
                </form>
                <div id="loginResponse" class="response" style="display: none;"></div>
            </div>
            
            <!-- Register Tab -->
            <div id="register-tab" class="tab-content">
                <form id="registerForm">
                    <div class="form-group">
                        <label for="registerName">Name:</label>
                        <input type="text" id="registerName" required>
                    </div>
                    <div class="form-group">
                        <label for="registerEmail">Email:</label>
                        <input type="email" id="registerEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="registerPassword">Password:</label>
                        <input type="password" id="registerPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="registerPasswordConfirm">Confirm Password:</label>
                        <input type="password" id="registerPasswordConfirm" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Register</button>
                </form>
                <div id="registerResponse" class="response" style="display: none;"></div>
            </div>
            
            <!-- Profile Tab -->
            <div id="profile-tab" class="tab-content">
                <div class="form-group">
                    <label for="authToken">Auth Token:</label>
                    <input type="text" id="authToken" placeholder="Login first to get token" readonly>
                </div>
                <button type="button" class="btn btn-primary" onclick="getProfile()">Get Profile</button>
                <button type="button" class="btn btn-secondary" onclick="logout()">Logout</button>
                <div id="profileResponse" class="response" style="display: none;"></div>
            </div>
        </div>
        
        <div class="info-section">
            <h2>🚀 API Testing</h2>
            <p>This interface allows you to test the authentication API endpoints. Use the demo credentials or register a new account.</p>
            
            <div class="demo-credentials">
                <h3>Demo Credentials</h3>
                <div class="credential-item">
                    <strong>Admin:</strong><br>
                    Email: <EMAIL><br>
                    Password: password
                </div>
                <div class="credential-item">
                    <strong>User:</strong><br>
                    Email: <EMAIL><br>
                    Password: password
                </div>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>🔗 API Endpoints</h3>
                <p>• POST /api/login</p>
                <p>• POST /api/register</p>
                <p>• GET /api/profile</p>
                <p>• POST /api/logout</p>
                <p>• GET /api/health</p>
            </div>
        </div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8000/api';
        let currentToken = null;

        function switchTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }

        async function makeRequest(url, method = 'GET', data = null, token = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                };

                if (token) {
                    options.headers['Authorization'] = `Bearer ${token}`;
                }

                if (data && ['POST', 'PUT', 'PATCH'].includes(method)) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(url, options);
                const responseData = await response.json();
                
                return {
                    status: response.status,
                    data: responseData,
                    success: response.ok
                };
            } catch (error) {
                return {
                    status: 0,
                    data: { error: error.message },
                    success: false
                };
            }
        }

        function displayResponse(elementId, response) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'response ' + (response.success ? 'success' : 'error');
            element.textContent = JSON.stringify(response, null, 2);
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            const response = await makeRequest(`${baseUrl}/login`, 'POST', {
                email: email,
                password: password
            });
            
            displayResponse('loginResponse', response);
            
            if (response.success && response.data.data && response.data.data.token) {
                currentToken = response.data.data.token;
                document.getElementById('authToken').value = currentToken;
            }
        });

        // Register form handler
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const passwordConfirm = document.getElementById('registerPasswordConfirm').value;
            
            const response = await makeRequest(`${baseUrl}/register`, 'POST', {
                name: name,
                email: email,
                password: password,
                password_confirmation: passwordConfirm
            });
            
            displayResponse('registerResponse', response);
        });

        async function getProfile() {
            const token = document.getElementById('authToken').value || currentToken;
            
            if (!token) {
                alert('Please login first to get a token');
                return;
            }
            
            const response = await makeRequest(`${baseUrl}/profile`, 'GET', null, token);
            displayResponse('profileResponse', response);
        }

        async function logout() {
            const token = document.getElementById('authToken').value || currentToken;
            
            const response = await makeRequest(`${baseUrl}/logout`, 'POST', null, token);
            displayResponse('profileResponse', response);
            
            if (response.success) {
                currentToken = null;
                document.getElementById('authToken').value = '';
            }
        }

        async function testHealth() {
            const response = await makeRequest(`${baseUrl}/health`);
            displayResponse('loginResponse', response);
        }
    </script>
</body>
</html>
