(()=>{var e,t;-1<navigator.platform.indexOf("Win")&&(document.getElementsByClassName("main-content")[0]&&(e=document.querySelector(".main-content"),new PerfectScrollbar(e)),document.getElementsByClassName("sidenav")[0]&&(e=document.querySelector(".sidenav"),new PerfectScrollbar(e)),document.getElementsByClassName("navbar-collapse")[0]&&(t=document.querySelector(".navbar:not(.navbar-expand-lg) .navbar-collapse"),new PerfectScrollbar(t)),document.getElementsByClassName("fixed-plugin")[0])&&(t=document.querySelector(".fixed-plugin"),new PerfectScrollbar(t))})(),document.getElementById("navbarBlur")&&navbarBlurOnScroll("navbarBlur");var allInputs,fixedPlugin,fixedPluginButton,fixedPluginButtonNav,fixedPluginCard,fixedPluginCloseButton,navbar,buttonNavbarFixed,tooltipTriggerList=[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')),tooltipList=tooltipTriggerList.map(function(e){return new bootstrap.Tooltip(e)});function focused(e){e.parentElement.classList.contains("input-group")&&e.parentElement.classList.add("focused")}function defocused(e){e.parentElement.classList.contains("input-group")&&e.parentElement.classList.remove("focused")}function setAttributes(t,s){Object.keys(s).forEach(function(e){t.setAttribute(e,s[e])})}function sidebarColor(e){var t=document.querySelector(".nav-link.active"),e=e.getAttribute("data-color");t.classList.contains("bg-gradient-primary")&&t.classList.remove("bg-gradient-primary"),t.classList.contains("bg-gradient-dark")&&t.classList.remove("bg-gradient-dark"),t.classList.contains("bg-gradient-info")&&t.classList.remove("bg-gradient-info"),t.classList.contains("bg-gradient-success")&&t.classList.remove("bg-gradient-success"),t.classList.contains("bg-gradient-warning")&&t.classList.remove("bg-gradient-warning"),t.classList.contains("bg-gradient-danger")&&t.classList.remove("bg-gradient-danger"),t.classList.add("bg-gradient-"+e)}function sidebarType(e){for(var t=e.parentElement.children,s=e.getAttribute("data-class"),n=document.querySelector("body"),a=document.querySelector("body:not(.dark-version)"),n=n.classList.contains("dark-version"),i=[],r=0;r<t.length;r++)t[r].classList.remove("active"),i.push(t[r].getAttribute("data-class"));e.classList.contains("active")?e.classList.remove("active"):e.classList.add("active");for(var l,o,c,d=document.querySelector(".sidenav"),r=0;r<i.length;r++)d.classList.remove(i[r]);if(d.classList.add(s),"bg-transparent"==s||"bg-white"==s){var u=document.querySelectorAll(".sidenav .text-white:not(.nav-link-text):not(.active)");for(let e=0;e<u.length;e++)u[e].classList.remove("text-white"),u[e].classList.add("text-dark")}else{var f=document.querySelectorAll(".sidenav .text-dark");for(let e=0;e<f.length;e++)f[e].classList.add("text-white"),f[e].classList.remove("text-dark")}if("bg-transparent"==s&&n){f=document.querySelectorAll(".navbar-brand .text-dark");for(let e=0;e<f.length;e++)f[e].classList.add("text-white"),f[e].classList.remove("text-dark")}"bg-transparent"!=s&&"bg-white"!=s||!a?(o=(l=document.querySelector(".navbar-brand-img")).src).includes("logo-ct-dark.png")&&(c=o.replace("logo-ct-dark","logo-ct"),l.src=c):(o=(l=document.querySelector(".navbar-brand-img")).src).includes("logo-ct.png")&&(c=o.replace("logo-ct","logo-ct-dark"),l.src=c),"bg-white"==s&&n&&(o=(l=document.querySelector(".navbar-brand-img")).src).includes("logo-ct.png")&&(c=o.replace("logo-ct","logo-ct-dark"),l.src=c)}function navbarFixed(e){var t=["position-sticky","blur","shadow-blur","mt-4","left-auto","top-1","z-index-sticky"],s=document.getElementById("navbarBlur");e.getAttribute("checked")?(s.classList.remove(...t),s.setAttribute("navbar-scroll","false"),navbarBlurOnScroll("navbarBlur"),e.removeAttribute("checked")):(s.classList.add(...t),s.setAttribute("navbar-scroll","true"),navbarBlurOnScroll("navbarBlur"),e.setAttribute("checked","true"))}function navbarMinimize(e){var t=document.getElementsByClassName("g-sidenav-show")[0];e.getAttribute("checked")?(t.classList.remove("g-sidenav-hidden"),t.classList.add("g-sidenav-pinned"),e.removeAttribute("checked")):(t.classList.remove("g-sidenav-pinned"),t.classList.add("g-sidenav-hidden"),e.setAttribute("checked","true"))}function navbarBlurOnScroll(n){if("true"===document.getElementById(n).getAttribute("data-scroll")){let e=document.getElementById(n);var a,n=!!e&&e.getAttribute("data-scroll");let t=["blur","shadow-blur","left-auto"],s=["shadow-none"];function i(){e.classList.add(...t),e.classList.remove(...s),l("blur")}function r(){e.classList.remove(...t),e.classList.add(...s),l("transparent")}function l(e){var t=document.querySelectorAll(".navbar-main .nav-link"),s=document.querySelectorAll(".navbar-main .sidenav-toggler-line");"blur"===e?(t.forEach(e=>{e.classList.remove("text-body")}),s.forEach(e=>{e.classList.add("bg-dark")})):"transparent"===e&&(t.forEach(e=>{e.classList.add("text-body")}),s.forEach(e=>{e.classList.remove("bg-dark")}))}window.onscroll=debounce("true"==n?function(){(5<window.scrollY?i:r)()}:function(){r()},10),-1<navigator.platform.indexOf("Win")&&(a=document.querySelector(".main-content"),"true"==n?a.addEventListener("ps-scroll-y",debounce(function(){(5<a.scrollTop?i:r)()},10)):a.addEventListener("ps-scroll-y",debounce(function(){r()},10)))}}function debounce(n,a,i){var r;return function(){var e=this,t=arguments,s=i&&!r;clearTimeout(r),r=setTimeout(function(){r=null,i||n.apply(e,t)},a),s&&n.apply(e,t)}}0!=document.querySelectorAll(".input-group").length&&(allInputs=document.querySelectorAll("input.form-control")).forEach(e=>setAttributes(e,{onfocus:"focused(this)",onfocusout:"defocused(this)"})),document.querySelector(".fixed-plugin")&&(fixedPlugin=document.querySelector(".fixed-plugin"),fixedPlugin=document.querySelector(".fixed-plugin"),fixedPluginButton=document.querySelector(".fixed-plugin-button"),fixedPluginButtonNav=document.querySelector(".fixed-plugin-button-nav"),fixedPluginCard=document.querySelector(".fixed-plugin .card"),fixedPluginCloseButton=document.querySelectorAll(".fixed-plugin-close-button"),navbar=document.getElementById("navbarBlur"),buttonNavbarFixed=document.getElementById("navbarFixed"),fixedPluginButton&&(fixedPluginButton.onclick=function(){fixedPlugin.classList.contains("show")?fixedPlugin.classList.remove("show"):fixedPlugin.classList.add("show")}),fixedPluginButtonNav&&(fixedPluginButtonNav.onclick=function(){fixedPlugin.classList.contains("show")?fixedPlugin.classList.remove("show"):fixedPlugin.classList.add("show")}),fixedPluginCloseButton.forEach(function(e){e.onclick=function(){fixedPlugin.classList.remove("show")}}),document.querySelector("body").onclick=function(e){e.target!=fixedPluginButton&&e.target!=fixedPluginButtonNav&&e.target.closest(".fixed-plugin .card")!=fixedPluginCard&&fixedPlugin.classList.remove("show")},navbar)&&"true"==navbar.getAttribute("data-scroll")&&buttonNavbarFixed&&buttonNavbarFixed.setAttribute("checked","true"),document.addEventListener("DOMContentLoaded",function(){[].slice.call(document.querySelectorAll(".toast")).map(function(e){return new bootstrap.Toast(e)});[].slice.call(document.querySelectorAll(".toast-btn")).map(function(t){t.addEventListener("click",function(){var e=document.getElementById(t.dataset.target);e&&bootstrap.Toast.getInstance(e).show()})})});var total=document.querySelectorAll(".nav-pills");function initNavs(){total.forEach(function(i,e){var r=document.createElement("div"),t=i.querySelector("li:first-child .nav-link").cloneNode();t.innerHTML="-",r.classList.add("moving-tab","position-absolute","nav-link"),r.appendChild(t),i.appendChild(r),i.getElementsByTagName("li").length;r.style.padding="0px",r.style.width=i.querySelector("li:nth-child(1)").offsetWidth+"px",r.style.transform="translate3d(0px, 0px, 0px)",r.style.transition=".5s ease",i.onmouseover=function(e){let a=getEventTarget(e).closest("li");if(a){let s=Array.from(a.closest("ul").children),n=s.indexOf(a)+1;i.querySelector("li:nth-child("+n+") .nav-link").onclick=function(){r=i.querySelector(".moving-tab");let e=0;if(i.classList.contains("flex-column")){for(var t=1;t<=s.indexOf(a);t++)e+=i.querySelector("li:nth-child("+t+")").offsetHeight;r.style.transform="translate3d(0px,"+e+"px, 0px)",r.style.height=i.querySelector("li:nth-child("+t+")").offsetHeight}else{for(t=1;t<=s.indexOf(a);t++)e+=i.querySelector("li:nth-child("+t+")").offsetWidth;r.style.transform="translate3d("+e+"px, 0px, 0px)",r.style.width=i.querySelector("li:nth-child("+n+")").offsetWidth+"px"}}}}})}function getEventTarget(e){return(e=e||window.event).target||e.srcElement}setTimeout(function(){initNavs()},100),window.addEventListener("resize",function(e){total.forEach(function(t,e){t.querySelector(".moving-tab").remove();var s=document.createElement("div"),n=t.querySelector(".nav-link.active").cloneNode(),a=(n.innerHTML="-",s.classList.add("moving-tab","position-absolute","nav-link"),s.appendChild(n),t.appendChild(s),s.style.padding="0px",s.style.transition=".5s ease",t.querySelector(".nav-link.active").parentElement);if(a){var i=Array.from(a.closest("ul").children),n=i.indexOf(a)+1;let e=0;if(t.classList.contains("flex-column")){for(var r=1;r<=i.indexOf(a);r++)e+=t.querySelector("li:nth-child("+r+")").offsetHeight;s.style.transform="translate3d(0px,"+e+"px, 0px)",s.style.width=t.querySelector("li:nth-child("+n+")").offsetWidth+"px",s.style.height=t.querySelector("li:nth-child("+r+")").offsetHeight}else{for(r=1;r<=i.indexOf(a);r++)e+=t.querySelector("li:nth-child("+r+")").offsetWidth;s.style.transform="translate3d("+e+"px, 0px, 0px)",s.style.width=t.querySelector("li:nth-child("+n+")").offsetWidth+"px"}}}),window.innerWidth<991?total.forEach(function(t,e){if(!t.classList.contains("flex-column")){t.classList.remove("flex-row"),t.classList.add("flex-column","on-resize");var s=t.querySelector(".nav-link.active").parentElement,n=Array.from(s.closest("ul").children);n.indexOf(s);let e=0;for(var a=1;a<=n.indexOf(s);a++)e+=t.querySelector("li:nth-child("+a+")").offsetHeight;var i=document.querySelector(".moving-tab");i.style.width=t.querySelector("li:nth-child(1)").offsetWidth+"px",i.style.transform="translate3d(0px,"+e+"px, 0px)"}}):total.forEach(function(t,e){if(t.classList.contains("on-resize")){t.classList.remove("flex-column","on-resize"),t.classList.add("flex-row");var s=t.querySelector(".nav-link.active").parentElement,n=Array.from(s.closest("ul").children),a=n.indexOf(s)+1;let e=0;for(var i=1;i<=n.indexOf(s);i++)e+=t.querySelector("li:nth-child("+i+")").offsetWidth;var r=document.querySelector(".moving-tab");r.style.transform="translate3d("+e+"px, 0px, 0px)",r.style.width=t.querySelector("li:nth-child("+a+")").offsetWidth+"px"}})}),window.innerWidth<991&&total.forEach(function(e,t){e.classList.contains("flex-row")&&(e.classList.remove("flex-row"),e.classList.add("flex-column","on-resize"))}),window.onload=function(){for(var e=document.querySelectorAll("input"),t=0;t<e.length;t++)e[t].addEventListener("focus",function(e){this.parentElement.classList.add("is-focused")},!1),e[t].onkeyup=function(e){""!=this.value?this.parentElement.classList.add("is-filled"):this.parentElement.classList.remove("is-filled")},e[t].addEventListener("focusout",function(e){""!=this.value&&this.parentElement.classList.add("is-filled"),this.parentElement.classList.remove("is-focused")},!1);for(var s=document.querySelectorAll(".btn"),t=0;t<s.length;t++)s[t].addEventListener("click",function(e){var t=e.target,s=t.querySelector(".ripple");(s=document.createElement("span")).classList.add("ripple"),s.style.width=s.style.height=Math.max(t.offsetWidth,t.offsetHeight)+"px",t.appendChild(s),s.style.left=e.offsetX-s.offsetWidth/2+"px",s.style.top=e.offsetY-s.offsetHeight/2+"px",s.classList.add("ripple"),setTimeout(function(){s.parentElement.removeChild(s)},600)},!1)};let iconNavbarSidenav=document.getElementById("iconNavbarSidenav"),iconSidenav=document.getElementById("iconSidenav"),sidenav=document.getElementById("sidenav-main"),body=document.getElementsByTagName("body")[0],className="g-sidenav-pinned";function toggleSidenav(){body.classList.contains(className)?(body.classList.remove(className),setTimeout(function(){sidenav.classList.remove("bg-white")},100),sidenav.classList.remove("bg-transparent")):(body.classList.add(className),sidenav.classList.add("bg-white"),sidenav.classList.remove("bg-transparent"),iconSidenav.classList.remove("d-none"))}iconNavbarSidenav&&iconNavbarSidenav.addEventListener("click",toggleSidenav),iconSidenav&&iconSidenav.addEventListener("click",toggleSidenav);let referenceButtons=document.querySelector("[data-class]");function navbarColorOnResize(){1200<window.innerWidth?referenceButtons?.classList.contains("active")&&"bg-transparent"===referenceButtons?.getAttribute("data-class")?sidenav.classList.remove("bg-white"):sidenav.classList.add("bg-white"):(sidenav.classList.add("bg-white"),sidenav.classList.remove("bg-transparent"))}function sidenavTypeOnResize(){var e=document.querySelectorAll('[onclick="sidebarType(this)"]');window.innerWidth<1200?e.forEach(function(e){e.classList.add("disabled")}):e.forEach(function(e){e.classList.remove("disabled")})}function darkMode(e){var t=document.getElementsByTagName("body")[0],s=document.querySelectorAll("div:not(.sidenav) > hr"),n=document.querySelectorAll("div:not(.bg-gradient-dark) hr"),a=document.querySelectorAll("button:not(.btn) > .text-dark"),i=document.querySelectorAll("span.text-dark, .breadcrumb .text-dark"),r=document.querySelectorAll("span.text-white, .breadcrumb .text-white"),l=document.querySelectorAll("strong.text-dark"),o=document.querySelectorAll("strong.text-white"),c=document.querySelectorAll("a.nav-link.text-dark"),d=document.querySelectorAll("a.nav-link.text-white"),u=document.querySelectorAll(".text-secondary"),f=document.querySelectorAll(".bg-gray-100"),v=document.querySelectorAll(".bg-gray-600"),g=document.querySelectorAll(".btn.btn-link.text-dark, .material-symbols-rounded.text-dark"),m=document.querySelectorAll(".btn.btn-link.text-white, .material-symbols-rounded.text-white"),h=document.querySelectorAll(".card.border"),b=document.querySelectorAll(".card.border.border-dark"),y=document.querySelectorAll("g");if(e.getAttribute("checked")){t.classList.remove("dark-version");for(L=0;L<s.length;L++)s[L].classList.contains("light")&&(s[L].classList.add("dark"),s[L].classList.remove("light"));for(L=0;L<n.length;L++)n[L].classList.contains("light")&&(n[L].classList.add("dark"),n[L].classList.remove("light"));for(L=0;L<a.length;L++)a[L].classList.contains("text-white")&&(a[L].classList.remove("text-white"),a[L].classList.add("text-dark"));for(L=0;L<r.length;L++)!r[L].classList.contains("text-white")||r[L].closest(".sidenav")||r[L].closest(".card.bg-gradient-dark")||(r[L].classList.remove("text-white"),r[L].classList.add("text-dark"));for(L=0;L<o.length;L++)o[L].classList.contains("text-white")&&(o[L].classList.remove("text-white"),o[L].classList.add("text-dark"));for(L=0;L<d.length;L++)d[L].classList.contains("text-white")&&!d[L].closest(".sidenav")&&(d[L].classList.remove("text-white"),d[L].classList.add("text-dark"));for(L=0;L<u.length;L++)u[L].classList.contains("text-white")&&(u[L].classList.remove("text-white"),u[L].classList.remove("opacity-8"),u[L].classList.add("text-dark"));for(L=0;L<v.length;L++)v[L].classList.contains("bg-gray-600")&&(v[L].classList.remove("bg-gray-600"),v[L].classList.add("bg-gray-100"));for(L=0;L<y.length;L++)y[L].hasAttribute("fill")&&y[L].setAttribute("fill","#252f40");for(L=0;L<m.length;L++)m[L].closest(".card.bg-gradient-dark")||(m[L].classList.remove("text-white"),m[L].classList.add("text-dark"));for(L=0;L<b.length;L++)b[L].classList.remove("border-dark");e.removeAttribute("checked")}else{t.classList.add("dark-version");for(var L=0;L<s.length;L++)s[L].classList.contains("dark")&&(s[L].classList.remove("dark"),s[L].classList.add("light"));for(var L=0;L<n.length;L++)n[L].classList.contains("dark")&&(n[L].classList.remove("dark"),n[L].classList.add("light"));for(var L=0;L<a.length;L++)a[L].classList.contains("text-dark")&&(a[L].classList.remove("text-dark"),a[L].classList.add("text-white"));for(var L=0;L<i.length;L++)i[L].classList.contains("text-dark")&&(i[L].classList.remove("text-dark"),i[L].classList.add("text-white"));for(var L=0;L<l.length;L++)l[L].classList.contains("text-dark")&&(l[L].classList.remove("text-dark"),l[L].classList.add("text-white"));for(var L=0;L<c.length;L++)c[L].classList.contains("text-dark")&&(c[L].classList.remove("text-dark"),c[L].classList.add("text-white"));for(var L=0;L<u.length;L++)u[L].classList.contains("text-secondary")&&(u[L].classList.remove("text-secondary"),u[L].classList.add("text-white"),u[L].classList.add("opacity-8"));for(var L=0;L<f.length;L++)f[L].classList.contains("bg-gray-100")&&(f[L].classList.remove("bg-gray-100"),f[L].classList.add("bg-gray-600"));for(var L=0;L<g.length;L++)g[L].classList.remove("text-dark"),g[L].classList.add("text-white");for(var L=0;L<y.length;L++)y[L].hasAttribute("fill")&&y[L].setAttribute("fill","#fff");for(var L=0;L<h.length;L++)h[L].classList.add("border-dark");e.setAttribute("checked","true")}}sidenav&&window.addEventListener("resize",navbarColorOnResize),window.addEventListener("resize",sidenavTypeOnResize),window.addEventListener("load",sidenavTypeOnResize);let indicators=document.querySelectorAll(".indicator"),sections=document.querySelectorAll("section");if(indicators){let t=()=>{var e=document.querySelector(".indicator.active");e&&e.classList.remove("active")},e=e=>{new IntersectionObserver(e=>{e.forEach(e=>{e.isIntersecting&&(t(),e=e.target,e=document.querySelector(`a[href='#${e.id}']`))&&e.classList.add("active")})},{root:null,rootMargin:"0px",threshold:.75}).observe(e)};indicators.forEach(e=>{e.addEventListener("click",function(e){e.preventDefault(),document.querySelector(this.getAttribute("href")).scrollIntoView({behavior:"smooth"}),t(),this.classList.add("active")})}),sections.forEach(e)}
//# sourceMappingURL=_site_dashboard_free/assets/js/dashboard-free.js.map