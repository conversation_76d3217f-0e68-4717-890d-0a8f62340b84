<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .response {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 REST API Test Interface</h1>
        <p>This interface allows you to test the REST API endpoints directly from your browser.</p>
        <p><strong>Base URL:</strong> <span id="baseUrl">http://localhost:8000/api</span></p>
    </div>

    <div class="container">
        <h2>🏥 Health Check</h2>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <div id="healthResponse" class="response" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>📋 Get All Items</h2>
        <button onclick="getAllItems()">Get All Items</button>
        <div id="getAllResponse" class="response" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>➕ Create New Item</h2>
        <div class="form-group">
            <label for="createName">Name:</label>
            <input type="text" id="createName" placeholder="Enter item name">
        </div>
        <div class="form-group">
            <label for="createDescription">Description:</label>
            <textarea id="createDescription" placeholder="Enter item description" rows="3"></textarea>
        </div>
        <button onclick="createItem()">Create Item</button>
        <div id="createResponse" class="response" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>🔍 Get Single Item</h2>
        <div class="form-group">
            <label for="getItemId">Item ID:</label>
            <input type="number" id="getItemId" placeholder="Enter item ID">
        </div>
        <button onclick="getSingleItem()">Get Item</button>
        <div id="getSingleResponse" class="response" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>✏️ Update Item</h2>
        <div class="form-group">
            <label for="updateItemId">Item ID:</label>
            <input type="number" id="updateItemId" placeholder="Enter item ID">
        </div>
        <div class="form-group">
            <label for="updateName">Name:</label>
            <input type="text" id="updateName" placeholder="Enter new name">
        </div>
        <div class="form-group">
            <label for="updateDescription">Description:</label>
            <textarea id="updateDescription" placeholder="Enter new description" rows="3"></textarea>
        </div>
        <button onclick="updateItem()">Update Item</button>
        <div id="updateResponse" class="response" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>🗑️ Delete Item</h2>
        <div class="form-group">
            <label for="deleteItemId">Item ID:</label>
            <input type="number" id="deleteItemId" placeholder="Enter item ID">
        </div>
        <button class="danger" onclick="deleteItem()">Delete Item</button>
        <div id="deleteResponse" class="response" style="display: none;"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8000/api';

        async function makeRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                };

                if (data && ['POST', 'PUT', 'PATCH'].includes(method)) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(url, options);
                const responseData = await response.json();
                
                return {
                    status: response.status,
                    data: responseData,
                    success: response.ok
                };
            } catch (error) {
                return {
                    status: 0,
                    data: { error: error.message },
                    success: false
                };
            }
        }

        function displayResponse(elementId, response) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'response ' + (response.success ? 'success' : 'error');
            element.textContent = JSON.stringify(response, null, 2);
        }

        async function testHealth() {
            const response = await makeRequest(`${baseUrl}/health`);
            displayResponse('healthResponse', response);
        }

        async function getAllItems() {
            const response = await makeRequest(`${baseUrl}/items`);
            displayResponse('getAllResponse', response);
        }

        async function createItem() {
            const name = document.getElementById('createName').value;
            const description = document.getElementById('createDescription').value;
            
            if (!name || !description) {
                alert('Please fill in both name and description');
                return;
            }

            const response = await makeRequest(`${baseUrl}/items`, 'POST', {
                name: name,
                description: description
            });
            displayResponse('createResponse', response);
            
            // Clear form if successful
            if (response.success) {
                document.getElementById('createName').value = '';
                document.getElementById('createDescription').value = '';
            }
        }

        async function getSingleItem() {
            const id = document.getElementById('getItemId').value;
            
            if (!id) {
                alert('Please enter an item ID');
                return;
            }

            const response = await makeRequest(`${baseUrl}/items/${id}`);
            displayResponse('getSingleResponse', response);
        }

        async function updateItem() {
            const id = document.getElementById('updateItemId').value;
            const name = document.getElementById('updateName').value;
            const description = document.getElementById('updateDescription').value;
            
            if (!id) {
                alert('Please enter an item ID');
                return;
            }

            const data = {};
            if (name) data.name = name;
            if (description) data.description = description;

            if (Object.keys(data).length === 0) {
                alert('Please enter at least one field to update');
                return;
            }

            const response = await makeRequest(`${baseUrl}/items/${id}`, 'PUT', data);
            displayResponse('updateResponse', response);
        }

        async function deleteItem() {
            const id = document.getElementById('deleteItemId').value;
            
            if (!id) {
                alert('Please enter an item ID');
                return;
            }

            if (!confirm('Are you sure you want to delete this item?')) {
                return;
            }

            const response = await makeRequest(`${baseUrl}/items/${id}`, 'DELETE');
            displayResponse('deleteResponse', response);
        }
    </script>
</body>
</html>
