<?php

/**
 * API Testing Script
 * 
 * This script tests the REST API endpoints to verify they're working correctly.
 * Make sure your Laravel development server is running before executing this script.
 * 
 * Usage: php test_api.php
 */

// Configuration
$baseUrl = 'http://localhost:8000/api'; // Adjust if your Laravel app runs on different port
$timeout = 30; // Request timeout in seconds

// Colors for console output
class Colors {
    const GREEN = "\033[32m";
    const RED = "\033[31m";
    const YELLOW = "\033[33m";
    const BLUE = "\033[34m";
    const RESET = "\033[0m";
}

/**
 * Make HTTP request using cURL
 */
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => array_merge([
            'Content-Type: application/json',
            'Accept: application/json'
        ], $headers)
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error, 'http_code' => 0];
    }
    
    return [
        'body' => $response,
        'http_code' => $httpCode,
        'data' => json_decode($response, true)
    ];
}

/**
 * Print test result
 */
function printResult($testName, $success, $message = '', $data = null) {
    $color = $success ? Colors::GREEN : Colors::RED;
    $status = $success ? 'PASS' : 'FAIL';
    
    echo $color . "[$status] " . Colors::RESET . $testName;
    if ($message) {
        echo " - " . $message;
    }
    echo "\n";
    
    if ($data && is_array($data)) {
        echo Colors::BLUE . "Response: " . Colors::RESET . json_encode($data, JSON_PRETTY_PRINT) . "\n";
    }
    echo "\n";
}

/**
 * Test API endpoint
 */
function testEndpoint($name, $url, $method = 'GET', $data = null, $expectedCode = 200) {
    echo Colors::YELLOW . "Testing: $name" . Colors::RESET . "\n";
    echo "URL: $url\n";
    echo "Method: $method\n";
    if ($data) {
        echo "Data: " . json_encode($data) . "\n";
    }
    echo "Expected HTTP Code: $expectedCode\n";
    echo str_repeat('-', 50) . "\n";
    
    $result = makeRequest($url, $method, $data);
    
    if (isset($result['error'])) {
        printResult($name, false, "cURL Error: " . $result['error']);
        return false;
    }
    
    $success = $result['http_code'] === $expectedCode;
    $message = "HTTP {$result['http_code']}";
    
    if (isset($result['data']['message'])) {
        $message .= " - " . $result['data']['message'];
    }
    
    printResult($name, $success, $message, $result['data']);
    
    return $success ? $result : false;
}

// Start testing
echo Colors::BLUE . "=== API Testing Script ===" . Colors::RESET . "\n";
echo "Base URL: $baseUrl\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

$testResults = [];

// Test 1: Health Check
$testResults['health'] = testEndpoint(
    'Health Check',
    "$baseUrl/health"
);

// Test 2: Get All Items
$testResults['get_all'] = testEndpoint(
    'Get All Items',
    "$baseUrl/items"
);

// Test 3: Create New Item
$newItem = [
    'name' => 'Test Item ' . time(),
    'description' => 'This is a test item created by the API test script'
];

$testResults['create'] = testEndpoint(
    'Create New Item',
    "$baseUrl/items",
    'POST',
    $newItem,
    201
);

// Test 4: Get Single Item (if create was successful)
if ($testResults['create'] && isset($testResults['create']['data']['data']['id'])) {
    $itemId = $testResults['create']['data']['data']['id'];
    $testResults['get_single'] = testEndpoint(
        'Get Single Item',
        "$baseUrl/items/$itemId"
    );
    
    // Test 5: Update Item
    $updateData = [
        'name' => 'Updated Test Item ' . time(),
        'description' => 'This item has been updated by the API test script'
    ];
    
    $testResults['update'] = testEndpoint(
        'Update Item',
        "$baseUrl/items/$itemId",
        'PUT',
        $updateData
    );
    
    // Test 6: Delete Item
    $testResults['delete'] = testEndpoint(
        'Delete Item',
        "$baseUrl/items/$itemId",
        'DELETE'
    );
} else {
    echo Colors::RED . "Skipping single item tests due to create failure\n\n" . Colors::RESET;
}

// Test 7: Get Non-existent Item (should return 404)
$testResults['not_found'] = testEndpoint(
    'Get Non-existent Item',
    "$baseUrl/items/99999",
    'GET',
    null,
    404
);

// Summary
echo Colors::BLUE . "=== Test Summary ===" . Colors::RESET . "\n";
$passed = 0;
$total = 0;

foreach ($testResults as $test => $result) {
    $total++;
    if ($result !== false) {
        $passed++;
        echo Colors::GREEN . "✓ " . Colors::RESET . ucfirst(str_replace('_', ' ', $test)) . "\n";
    } else {
        echo Colors::RED . "✗ " . Colors::RESET . ucfirst(str_replace('_', ' ', $test)) . "\n";
    }
}

echo "\nResults: $passed/$total tests passed\n";

if ($passed === $total) {
    echo Colors::GREEN . "🎉 All tests passed! Your API is working correctly." . Colors::RESET . "\n";
} else {
    echo Colors::RED . "❌ Some tests failed. Check your API implementation." . Colors::RESET . "\n";
}

echo "\nNote: Make sure your Laravel development server is running with 'php artisan serve'\n";
