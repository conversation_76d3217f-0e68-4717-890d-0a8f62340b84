<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class ApiTestController extends Controller
{
    // Demo users for testing (in real app, use database)
    private static $users = [
        [
            'id' => 1,
            'email' => '<EMAIL>',
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'name' => 'Admin User',
            'role' => 'admin'
        ],
        [
            'id' => 2,
            'email' => '<EMAIL>',
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'name' => 'Regular User',
            'role' => 'user'
        ]
    ];

    /**
     * Handle login request
     */
    public function login(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string|min:6'
        ]);

        // Find user by email
        $user = collect(self::$users)->firstWhere('email', $request->email);

        if (!$user || !Hash::check($request->password, $user['password'])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        // Generate a simple token (in real app, use Laravel Sanctum or JWT)
        $token = base64_encode($user['email'] . ':' . time());

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ],
                'token' => $token,
                'expires_in' => 3600 // 1 hour
            ]
        ]);
    }

    /**
     * Handle logout request
     */
    public function logout(Request $request): JsonResponse
    {
        // In a real app, you would invalidate the token here
        return response()->json([
            'success' => true,
            'message' => 'Logout successful'
        ]);
    }

    /**
     * Get user profile (protected route)
     */
    public function profile(Request $request): JsonResponse
    {
        $token = $request->bearerToken();

        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'Token required'
            ], 401);
        }

        // Simple token validation (in real app, use proper token validation)
        try {
            $decoded = base64_decode($token);
            $parts = explode(':', $decoded);
            $email = $parts[0] ?? null;

            $user = collect(self::$users)->firstWhere('email', $email);

            if (!$user) {
                throw new \Exception('Invalid token');
            }

            return response()->json([
                'success' => true,
                'message' => 'Profile retrieved successfully',
                'data' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid token'
            ], 401);
        }
    }

    /**
     * Register new user
     */
    public function register(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'password' => 'required|string|min:6|confirmed'
        ]);

        // Check if email already exists
        $existingUser = collect(self::$users)->firstWhere('email', $request->email);

        if ($existingUser) {
            return response()->json([
                'success' => false,
                'message' => 'Email already exists'
            ], 422);
        }

        // Create new user
        $newUser = [
            'id' => count(self::$users) + 1,
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'user'
        ];

        self::$users[] = $newUser;

        return response()->json([
            'success' => true,
            'message' => 'User registered successfully',
            'data' => [
                'id' => $newUser['id'],
                'name' => $newUser['name'],
                'email' => $newUser['email'],
                'role' => $newUser['role']
            ]
        ], 201);
    }

    /**
     * Simple health check endpoint
     */
    public function health(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => 'Authentication API is working!',
            'timestamp' => now()->toISOString(),
            'version' => '1.0.0',
            'demo_users' => [
                ['email' => '<EMAIL>', 'password' => 'password', 'role' => 'admin'],
                ['email' => '<EMAIL>', 'password' => 'password', 'role' => 'user']
            ]
        ]);
    }
}
