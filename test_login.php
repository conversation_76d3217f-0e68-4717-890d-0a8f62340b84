<?php

/**
 * Login API Testing Script
 * 
 * This script tests the authentication endpoints to verify login functionality.
 * Make sure your Laravel development server is running before executing this script.
 * 
 * Usage: php test_login.php
 */

// Configuration
$baseUrl = 'http://localhost:8000/api'; // Adjust if your Laravel app runs on different port

// Colors for console output
class Colors {
    const GREEN = "\033[32m";
    const RED = "\033[31m";
    const YELLOW = "\033[33m";
    const BLUE = "\033[34m";
    const CYAN = "\033[36m";
    const RESET = "\033[0m";
}

/**
 * Make HTTP request using cURL
 */
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => array_merge([
            'Content-Type: application/json',
            'Accept: application/json'
        ], $headers)
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error, 'http_code' => 0];
    }
    
    return [
        'body' => $response,
        'http_code' => $httpCode,
        'data' => json_decode($response, true)
    ];
}

/**
 * Print test result
 */
function printResult($testName, $success, $message = '', $data = null) {
    $color = $success ? Colors::GREEN : Colors::RED;
    $status = $success ? 'PASS' : 'FAIL';
    
    echo $color . "[$status] " . Colors::RESET . $testName;
    if ($message) {
        echo " - " . $message;
    }
    echo "\n";
    
    if ($data && is_array($data)) {
        echo Colors::CYAN . "Response: " . Colors::RESET . json_encode($data, JSON_PRETTY_PRINT) . "\n";
    }
    echo "\n";
}

/**
 * Test API endpoint
 */
function testEndpoint($name, $url, $method = 'GET', $data = null, $expectedCode = 200, $headers = []) {
    echo Colors::YELLOW . "Testing: $name" . Colors::RESET . "\n";
    echo "URL: $url\n";
    echo "Method: $method\n";
    if ($data) {
        echo "Data: " . json_encode($data) . "\n";
    }
    echo "Expected HTTP Code: $expectedCode\n";
    echo str_repeat('-', 50) . "\n";
    
    $result = makeRequest($url, $method, $data, $headers);
    
    if (isset($result['error'])) {
        printResult($name, false, "cURL Error: " . $result['error']);
        return false;
    }
    
    $success = $result['http_code'] === $expectedCode;
    $message = "HTTP {$result['http_code']}";
    
    if (isset($result['data']['message'])) {
        $message .= " - " . $result['data']['message'];
    }
    
    printResult($name, $success, $message, $result['data']);
    
    return $success ? $result : false;
}

// Start testing
echo Colors::BLUE . "=== Login API Testing Script ===" . Colors::RESET . "\n";
echo "Base URL: $baseUrl\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

$testResults = [];
$authToken = null;

// Test 1: Health Check
echo Colors::CYAN . "🏥 Testing Health Check..." . Colors::RESET . "\n";
$testResults['health'] = testEndpoint(
    'Health Check',
    "$baseUrl/health"
);

// Test 2: Valid Login
echo Colors::CYAN . "🔐 Testing Valid Login..." . Colors::RESET . "\n";
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password'
];

$testResults['valid_login'] = testEndpoint(
    'Valid Login',
    "$baseUrl/login",
    'POST',
    $loginData
);

// Extract token for further tests
if ($testResults['valid_login'] && isset($testResults['valid_login']['data']['data']['token'])) {
    $authToken = $testResults['valid_login']['data']['data']['token'];
    echo Colors::GREEN . "✓ Token extracted: " . substr($authToken, 0, 20) . "..." . Colors::RESET . "\n\n";
}

// Test 3: Invalid Login
echo Colors::CYAN . "❌ Testing Invalid Login..." . Colors::RESET . "\n";
$invalidLoginData = [
    'email' => '<EMAIL>',
    'password' => 'wrongpassword'
];

$testResults['invalid_login'] = testEndpoint(
    'Invalid Login',
    "$baseUrl/login",
    'POST',
    $invalidLoginData,
    401
);

// Test 4: Login with Missing Fields
echo Colors::CYAN . "📝 Testing Login with Missing Fields..." . Colors::RESET . "\n";
$incompleteLoginData = [
    'email' => '<EMAIL>'
    // missing password
];

$testResults['incomplete_login'] = testEndpoint(
    'Incomplete Login Data',
    "$baseUrl/login",
    'POST',
    $incompleteLoginData,
    422
);

// Test 5: User Registration
echo Colors::CYAN . "👤 Testing User Registration..." . Colors::RESET . "\n";
$registerData = [
    'name' => 'Test User',
    'email' => 'testuser' . time() . '@example.com',
    'password' => 'password123',
    'password_confirmation' => 'password123'
];

$testResults['register'] = testEndpoint(
    'User Registration',
    "$baseUrl/register",
    'POST',
    $registerData,
    201
);

// Test 6: Profile Access with Token
if ($authToken) {
    echo Colors::CYAN . "👤 Testing Profile Access with Token..." . Colors::RESET . "\n";
    $testResults['profile_with_token'] = testEndpoint(
        'Profile with Valid Token',
        "$baseUrl/profile",
        'GET',
        null,
        200,
        ["Authorization: Bearer $authToken"]
    );
} else {
    echo Colors::RED . "Skipping profile test - no auth token available\n\n" . Colors::RESET;
}

// Test 7: Profile Access without Token
echo Colors::CYAN . "🚫 Testing Profile Access without Token..." . Colors::RESET . "\n";
$testResults['profile_no_token'] = testEndpoint(
    'Profile without Token',
    "$baseUrl/profile",
    'GET',
    null,
    401
);

// Test 8: Logout
if ($authToken) {
    echo Colors::CYAN . "🚪 Testing Logout..." . Colors::RESET . "\n";
    $testResults['logout'] = testEndpoint(
        'Logout',
        "$baseUrl/logout",
        'POST',
        null,
        200,
        ["Authorization: Bearer $authToken"]
    );
} else {
    echo Colors::RED . "Skipping logout test - no auth token available\n\n" . Colors::RESET;
}

// Test 9: Second User Login
echo Colors::CYAN . "👥 Testing Second User Login..." . Colors::RESET . "\n";
$secondUserLogin = [
    'email' => '<EMAIL>',
    'password' => 'password'
];

$testResults['second_user_login'] = testEndpoint(
    'Second User Login',
    "$baseUrl/login",
    'POST',
    $secondUserLogin
);

// Summary
echo Colors::BLUE . "=== Test Summary ===" . Colors::RESET . "\n";
$passed = 0;
$total = 0;

foreach ($testResults as $test => $result) {
    $total++;
    if ($result !== false) {
        $passed++;
        echo Colors::GREEN . "✓ " . Colors::RESET . ucfirst(str_replace('_', ' ', $test)) . "\n";
    } else {
        echo Colors::RED . "✗ " . Colors::RESET . ucfirst(str_replace('_', ' ', $test)) . "\n";
    }
}

echo "\nResults: $passed/$total tests passed\n";

if ($passed === $total) {
    echo Colors::GREEN . "🎉 All tests passed! Your login API is working correctly." . Colors::RESET . "\n";
} else {
    echo Colors::RED . "❌ Some tests failed. Check your API implementation." . Colors::RESET . "\n";
}

echo "\n" . Colors::BLUE . "Demo Credentials:" . Colors::RESET . "\n";
echo "Admin: <EMAIL> / password\n";
echo "User: <EMAIL> / password\n";
echo "\nNote: Make sure your Laravel development server is running with 'php artisan serve'\n";
